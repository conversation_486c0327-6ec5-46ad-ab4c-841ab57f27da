package de.ktb.sbproto.users.services;

import de.ktb.sbproto.users.persistence.repositories.UserRepository;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserService {

    private final UserRepository userRepository;

    UserService(UserRepository uRepo) {
        this.userRepository = uRepo;
    }

    public List<String> getAllUsers() {
        return userRepository.findAll().stream().map(user -> user.getUsername()).toList();
    }
}
