# Test configuration with H2 in-memory database
spring.application.name=sbproto-test
server.port=0

# H2 Database Configuration for Tests
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA Configuration for Tests
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Disable Liquibase for tests
spring.liquibase.enabled=false

# H2 Console (optional for debugging)
spring.h2.console.enabled=true
