package de.ktb.sbproto.context;

import de.ktb.sbproto.users.controller.UserController;
import de.ktb.sbproto.users.services.UserService;
import de.ktb.sbproto.users.persistence.repositories.UserRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Context test that verifies:
 * 1. UserController is properly instantiated as a Spring bean
 * 2. All dependencies are correctly wired
 * 3. Component scanning works for the users module
 * 4. Application context loads successfully
 */
@SpringBootTest
@ActiveProfiles("test")
class UserControllerContextTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private UserController userController;

    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;

    @Test
    void shouldLoadApplicationContext() {
        assertThat(applicationContext).isNotNull();
    }

    @Test
    void shouldInstantiateUserController() {
        assertThat(userController).isNotNull();
        assertThat(applicationContext.getBean(UserController.class)).isNotNull();
    }

    @Test
    void shouldInstantiateUserService() {
        assertThat(userService).isNotNull();
        assertThat(applicationContext.getBean(UserService.class)).isNotNull();
    }

    @Test
    void shouldInstantiateUserRepository() {
        assertThat(userRepository).isNotNull();
        assertThat(applicationContext.getBean(UserRepository.class)).isNotNull();
    }

    @Test
    void shouldHaveUserControllerAsRestController() {
        // Verify that UserController is annotated as @RestController
        assertThat(userController.getClass().isAnnotationPresent(
            org.springframework.web.bind.annotation.RestController.class)).isTrue();
    }

    @Test
    void shouldHaveUserServiceAsService() {
        // Verify that UserService is annotated as @Service
        assertThat(userService.getClass().isAnnotationPresent(
            org.springframework.stereotype.Service.class)).isTrue();
    }

    @Test
    void shouldWireDependenciesCorrectly() {
        // Verify that the controller has the service injected
        assertThat(userController).isNotNull();
        assertThat(userService).isNotNull();

        // We can't directly access private fields, but we can verify the beans exist
        // and the context loaded successfully, which means dependency injection worked
    }

    @Test
    void shouldHaveCorrectBeanNames() {
        // Verify beans can be retrieved by name
        assertThat(applicationContext.getBean("userController")).isNotNull();
        assertThat(applicationContext.getBean("userService")).isNotNull();
        assertThat(applicationContext.getBean("userRepository")).isNotNull();
    }

    @Test
    void shouldHaveCorrectBeanTypes() {
        // Verify bean types
        assertThat(applicationContext.getBean("userController")).isInstanceOf(UserController.class);
        assertThat(applicationContext.getBean("userService")).isInstanceOf(UserService.class);
        assertThat(applicationContext.getBean("userRepository")).isInstanceOf(UserRepository.class);
    }

    @Test
    void shouldHaveSingletonBeans() {
        // Verify that beans are singletons (default Spring behavior)
        UserController controller1 = applicationContext.getBean(UserController.class);
        UserController controller2 = applicationContext.getBean(UserController.class);
        assertThat(controller1).isSameAs(controller2);

        UserService service1 = applicationContext.getBean(UserService.class);
        UserService service2 = applicationContext.getBean(UserService.class);
        assertThat(service1).isSameAs(service2);
    }
}
