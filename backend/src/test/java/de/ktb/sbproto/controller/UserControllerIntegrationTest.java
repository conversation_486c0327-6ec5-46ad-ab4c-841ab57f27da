package de.ktb.sbproto.controller;

import de.ktb.sbproto.TestcontainersConfiguration;
import de.ktb.sbproto.users.persistence.dao.UserDao;
import de.ktb.sbproto.users.persistence.repositories.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.hamcrest.Matchers.*;

/**
 * Integration test for UserController that verifies:
 * 1. Controller is properly instantiated and mapped
 * 2. Full application context loads correctly
 * 3. Database integration works
 * 4. HTTP endpoints respond correctly
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@Import(TestcontainersConfiguration.class)
@Transactional
class UserControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private UserRepository userRepository;

    @BeforeEach
    void setUp() {
        // Clean up any existing data
        userRepository.deleteAll();

        // Create test data
        UserDao user1 = new UserDao();
        user1.setUsername("testuser1");
        user1.setPassword("password1");

        UserDao user2 = new UserDao();
        user2.setUsername("testuser2");
        user2.setPassword("password2");

        userRepository.save(user1);
        userRepository.save(user2);
    }

    @Test
    void shouldLoadApplicationContext() {
        // This test verifies that the application context loads successfully
        // and all beans are properly instantiated
    }

    @Test
    void shouldReturnUsersWhenGetAllUsersEndpointCalled() throws Exception {
        mockMvc.perform(get("/api/v1/users"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0]", is("testuser1")))
                .andExpect(jsonPath("$[1]", is("testuser2")));
    }

    @Test
    void shouldReturnEmptyListWhenNoUsersExist() throws Exception {
        // Clear all users
        userRepository.deleteAll();

        mockMvc.perform(get("/api/v1/users"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(0)));
    }

    @Test
    void shouldHandleControllerInstantiation() throws Exception {
        // Verify that the controller is properly instantiated and accessible
        mockMvc.perform(get("/api/v1/users"))
                .andExpect(status().isOk());
    }
}
