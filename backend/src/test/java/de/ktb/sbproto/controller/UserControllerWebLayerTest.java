package de.ktb.sbproto.controller;

import de.ktb.sbproto.users.controller.UserController;
import de.ktb.sbproto.users.services.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.hamcrest.Matchers.*;

/**
 * Web layer test for UserController that verifies:
 * 1. Controller is properly instantiated and mapped
 * 2. HTTP request mapping works correctly
 * 3. Response serialization works
 * 4. Controller layer logic without database dependencies
 */
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@DisabledInAotMode
class UserControllerWebLayerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserService userService;

    @Test
    void shouldReturnUsersFromService() throws Exception {
        // Given
        List<String> mockUsers = Arrays.asList("user1", "user2", "user3");
        when(userService.getAllUsers()).thenReturn(mockUsers);

        // When & Then
        mockMvc.perform(get("/api/v1/users"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(3)))
                .andExpect(jsonPath("$[0]", is("user1")))
                .andExpect(jsonPath("$[1]", is("user2")))
                .andExpect(jsonPath("$[2]", is("user3")));
    }

    @Test
    void shouldReturnEmptyListWhenServiceReturnsEmpty() throws Exception {
        // Given
        when(userService.getAllUsers()).thenReturn(Collections.emptyList());

        // When & Then
        mockMvc.perform(get("/api/v1/users"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$", hasSize(0)));
    }

    @Test
    void shouldHaveCorrectRequestMapping() throws Exception {
        // Given
        when(userService.getAllUsers()).thenReturn(Arrays.asList("testuser"));

        // When & Then - Test the exact endpoint path
        mockMvc.perform(get("/api/v1/users"))
                .andExpect(status().isOk());
    }

    @Test
    void shouldReturn404ForIncorrectPath() throws Exception {
        // When & Then - Test that incorrect paths return 404
        mockMvc.perform(get("/api/v1/user"))  // Note: singular "user" instead of "users"
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldReturn404ForIncorrectApiVersion() throws Exception {
        // When & Then - Test that incorrect API version returns 404
        mockMvc.perform(get("/api/v2/users"))
                .andExpect(status().isNotFound());
    }
}
