package de.ktb.sbproto.controller;

import de.ktb.sbproto.users.controller.UserController;
import de.ktb.sbproto.users.services.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;

/**
 * Unit test for UserController that verifies:
 * 1. Controller can be instantiated with dependencies
 * 2. Controller logic works correctly
 * 3. Service interactions are correct
 * 4. Return values are properly handled
 */
@ExtendWith(MockitoExtension.class)
class UserControllerUnitTest {

    @Mock
    private UserService userService;

    private UserController userController;

    @BeforeEach
    void setUp() {
        userController = new UserController(userService);
    }

    @Test
    void shouldInstantiateControllerWithUserService() {
        // Given & When
        UserController controller = new UserController(userService);

        // Then
        assertThat(controller).isNotNull();
    }

    @Test
    void shouldReturnUsersFromService() {
        // Given
        List<String> expectedUsers = Arrays.asList("user1", "user2", "user3");
        when(userService.getAllUsers()).thenReturn(expectedUsers);

        // When
        List<String> actualUsers = userController.getAllUsers();

        // Then
        assertThat(actualUsers).isEqualTo(expectedUsers);
        assertThat(actualUsers).hasSize(3);
        assertThat(actualUsers).containsExactly("user1", "user2", "user3");
        verify(userService).getAllUsers();
    }

    @Test
    void shouldReturnEmptyListWhenServiceReturnsEmpty() {
        // Given
        when(userService.getAllUsers()).thenReturn(Collections.emptyList());

        // When
        List<String> actualUsers = userController.getAllUsers();

        // Then
        assertThat(actualUsers).isEmpty();
        verify(userService).getAllUsers();
    }

    @Test
    void shouldCallServiceExactlyOnce() {
        // Given
        when(userService.getAllUsers()).thenReturn(Arrays.asList("testuser"));

        // When
        userController.getAllUsers();

        // Then
        verify(userService).getAllUsers();
    }

    @Test
    void shouldHandleNullResponseFromService() {
        // Given
        when(userService.getAllUsers()).thenReturn(null);

        // When
        List<String> actualUsers = userController.getAllUsers();

        // Then
        assertThat(actualUsers).isNull();
        verify(userService).getAllUsers();
    }
}
