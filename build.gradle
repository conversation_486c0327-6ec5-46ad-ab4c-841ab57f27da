plugins {
    id 'base'
    id 'io.spring.dependency-management' version '1.1.7' apply false
}

group = 'de.ktb'
version = '0.0.1-SNAPSHOT'


allprojects {

    ext {
        springBootVersion = '3.5.0'
    }

    repositories {
        mavenLocal()
        mavenCentral()
        google()
        maven { url "https://jitpack.io" }
        maven { url "https://plugins.gradle.org/m2/" }
        maven { url "https://repo.spring.io/milestone/" }
    }
}

subprojects {
    apply plugin: 'java'
    apply plugin: 'idea'
    apply plugin: 'io.spring.dependency-management'

    dependencyManagement {
        imports {
            mavenBom("org.springframework.boot:spring-boot-dependencies:${springBootVersion}")
        }
    }

    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(21)
        }
    }

    tasks.withType(Test) {
        useJUnitPlatform()
    }
}

// Root project doesn't need dependencies as it's just a container for modules
dependencies {
    // No dependencies needed at root level
}
